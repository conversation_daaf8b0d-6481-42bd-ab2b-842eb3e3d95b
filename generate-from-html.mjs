/**
 * {{ AURA-X: Add - 新增 HTML → JSON/JS 生成脚本。Approval: 寸止(ID:1699999999). Confirmed via 寸止 }}
 *
 * 功能：
 * 1) 读取项目根目录下的 `index.html`
 * 2) 基于 `text.json` 的结构参考，将 HTML 中的文本类元素映射为组件 JSON 对象
 *    - 支持的元素：具有 `data-comp="text"` 的任意元素，以及 p/span/a/button/h1~h6（作为回退）
 *    - a 标签会尝试映射 href/target 到 props.href
 * 3) 合并 `content/*.css` 为 `output/styles.css`
 * 4) 输出：
 *    - `output/components.json`：组件数组 JSON
 *    - `output/components.js`：ESM 导出 `components` 常量
 *    - `output/styles.css`：内容为合并后的样式
 *
 * 说明：
 * - 本脚本不依赖外部 npm 包，在 Node.js 环境下直接运行：`node generate-from-html.mjs`
 * - 若 `index.html` 为空或未匹配到元素，将生成空数组输出
 */

import { promises as fs } from 'node:fs';
import path from 'node:path';
import { pathToFileURL } from 'node:url';

/** 工具函数：安全读取文本文件（UTF-8），不存在时返回空字符串 */
async function readFileTextSafe(filePath) {
  try {
    return await fs.readFile(filePath, 'utf-8');
  } catch {
    return '';
  }
}

/** 工具函数：确保目录存在 */
async function ensureDirectory(directoryPath) {
  await fs.mkdir(directoryPath, { recursive: true });
}

/** 简易属性解析：从标签属性字符串中提取 key/value（支持双/单引号） */
function parseAttributes(attributeSource) {
  const attributes = {};
  if (!attributeSource) return attributes;
  // 提取形如 key="value" 或 key='value' 的属性
  const regex = /(\w[\w:-]*)\s*=\s*("([^"]*)"|'([^']*)')/g;
  let match;
  while ((match = regex.exec(attributeSource)) !== null) {
    const key = match[1];
    const value = match[3] !== undefined ? match[3] : match[4] !== undefined ? match[4] : '';
    attributes[key] = value;
  }
  return attributes;
}

/** 去除 HTML 标签，保留纯文本 */
function stripHtmlTags(html) {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
}

/** 根据 URL 判断 href 类型 */
function detectHrefType(url) {
  if (!url) return 'none';
  if (url.startsWith('mailto:')) return 'email';
  if (url.startsWith('tel:')) return 'tel';
  return 'url';
}

/** 读取 text.json 模板，失败则提供内置兜底模板 */
async function loadTextComponentTemplate(projectRoot) {
  const templatePath = path.join(projectRoot, 'text.json');
  const raw = await readFileTextSafe(templatePath);
  if (raw) {
    try {
      const obj = JSON.parse(raw);
      return obj;
    } catch {
      // fallthrough to default
    }
  }
  const nowSec = Math.floor(Date.now() / 1000);
  return {
    id: 'text',
    type: 'element/base',
    updatedAt: nowSec,
    createdAt: nowSec,
    version: 1,
    description: '文本组件（内置模板）',
    standalone: false,
    scope: 'isolate',
    props: {
      text: '',
      href: { type: 'none', value: '', target: '' },
      prompt: '',
      aiSet: { contentKeywords: '', type: 1 },
      dense: ''
    },
    propsMappings: {
      text: { dataType: ['string'], defaultFieldBind: 'text', refType: 'props, pageParams, dataSource, parentScope' },
      href: { dataType: ['object'], defaultFieldBind: 'link', refType: 'props, pageParams, dataSource, parentScope' },
      prompt: { dataType: ['string'], defaultFieldBind: 'placeholder', refType: 'props, pageParams, dataSource, parentScope' }
    },
    styles: {
      '*': '.${elemId} {\n    line-height: normal;\n}',
      'screen and (max-width:768px)': '',
      'screen and (min-width: 769px)': '',
      'screen and (max-width: 1024px) and (min-width: 769px)': ''
    },
    i18n: {
      'zh-CN': { text: '文本组件', description: '用于展示文本内容，支持链接配置和AI生成' },
      'en-US': { text: 'Text Component', description: 'Display text content with link configuration and AI generation support' }
    },
    template: '<p class="${elemId} ${styleClass}">\n    {{#if ${href}.value}}\n        <a href="{{$transferLink ${href} compId}}" {{#eq ${href}.type \"email\"}}rel="nofollow"{{/eq}}{{#eq ${href}.type \"tel\"}}rel="nofollow"{{/eq}} target="{{${href}.target}}">\n    {{/if}}\n    {{#if ${text}}}\n        {{$handleText ${text} ${dense}}}\n    {{^}}\n        {{${prompt}}}\n    {{/if}}\n    {{#if ${href}.value}}\n        </a>\n    {{/if}}\n</p>'
  };
}

/**
 * 解析 HTML，抽取文本类组件：
 * - data-comp="text" 的元素
 * - p/span/a/button/h1~h6（作为回退映射）
 */
function extractTextLikeElements(html) {
  const results = [];
  if (!html) return results;

  // 1) 优先：任何包含 data-comp="text" 的元素（宽松匹配）
  const dataCompRegex = /<([a-zA-Z][a-zA-Z0-9:-]*)\b([^>]*?)\bdata-comp\s*=\s*("text"|'text')([^>]*)>([\s\S]*?)<\/\1>/g;
  let match;
  while ((match = dataCompRegex.exec(html)) !== null) {
    const tag = match[1];
    const attrLeft = match[2] || '';
    const attrRight = match[4] || '';
    const attrStr = `${attrLeft} ${attrRight}`.trim();
    const attrs = parseAttributes(attrStr);
    const inner = match[5] || '';
    results.push({ tag, attrs, innerHtml: inner, source: 'data-comp' });
  }

  // 2) 回退：常见文本标签（避免重复：仅当未包含在 data-comp 结果中时再匹配）
  const fallbackRegex = /<(p|span|a|button|h[1-6])\b([^>]*)>([\s\S]*?)<\/\1>/gi;
  while ((match = fallbackRegex.exec(html)) !== null) {
    const tag = match[1];
    const attrs = parseAttributes(match[2] || '');
    const inner = match[3] || '';
    // 粗略去重：若该片段包含 data-comp="text"，上一步已收集
    const localHasDataComp = /\bdata-comp\s*=\s*("text"|'text')/.test(match[0]);
    if (localHasDataComp) continue;
    results.push({ tag, attrs, innerHtml: inner, source: 'fallback' });
  }

  return results;
}

/** 使用模板生成一个文本组件对象 */
function buildTextComponentFromTemplate(template, options) {
  const nowSec = Math.floor(Date.now() / 1000);
  const cloned = JSON.parse(JSON.stringify(template));
  cloned.updatedAt = nowSec;
  if (!cloned.createdAt) cloned.createdAt = nowSec;

  // props 映射
  const textContent = options.textContent ?? '';
  const hrefValue = options.hrefValue ?? '';
  const hrefTarget = options.hrefTarget ?? '';
  const hrefType = detectHrefType(hrefValue);

  if (!cloned.props) cloned.props = {};
  cloned.props.text = textContent;
  cloned.props.href = { type: hrefType || 'none', value: hrefValue || '', target: hrefTarget || '' };

  // 附加 meta，便于溯源（不影响模板渲染）
  cloned.meta = {
    sourceTag: options.sourceTag,
    source: options.source,
    class: options.className || '',
    id: options.id || ''
  };

  return cloned;
}

/** 合并 content 下所有 .css 到 output/styles.css */
async function mergeContentCssToOutput(contentDir, outputDir) {
  const files = await fs.readdir(contentDir).catch(() => []);
  const cssFiles = files.filter((f) => f.toLowerCase().endsWith('.css'));
  let merged = '';
  // 固定顺序：global -> pc -> pad -> mobile -> 其它
  const priority = ['global.css', 'pc.css', 'pad.css', 'mobile.css'];
  const ordered = [
    ...priority.filter((p) => cssFiles.includes(p)),
    ...cssFiles.filter((f) => !priority.includes(f))
  ];

  for (const css of ordered) {
    const full = path.join(contentDir, css);
    const txt = await readFileTextSafe(full);
    if (txt.trim()) {
      merged += `/* ===== ${css} ===== */\n` + txt.trim() + '\n\n';
    }
  }

  await ensureDirectory(outputDir);
  await fs.writeFile(path.join(outputDir, 'styles.css'), merged, 'utf-8');
}

async function main() {
  const projectRoot = process.cwd();
  const indexHtmlPath = path.join(projectRoot, 'index.html');
  const contentDir = path.join(projectRoot, 'content');
  const outputDir = path.join(projectRoot, 'output');

  const [html, template] = await Promise.all([
    readFileTextSafe(indexHtmlPath),
    loadTextComponentTemplate(projectRoot)
  ]);

  const elements = extractTextLikeElements(html);

  const components = [];
  for (const el of elements) {
    const textContent = stripHtmlTags(el.innerHtml);
    const hrefValue = el.attrs.href || el.attrs['data-href'] || '';
    const hrefTarget = el.attrs.target || el.attrs['data-target'] || '';
    const className = el.attrs.class || '';
    const elementId = el.attrs.id || '';

    const comp = buildTextComponentFromTemplate(template, {
      textContent,
      hrefValue,
      hrefTarget,
      className,
      id: elementId,
      sourceTag: el.tag,
      source: el.source
    });
    components.push(comp);
  }

  await ensureDirectory(outputDir);

  // 写入 JSON
  const jsonPath = path.join(outputDir, 'components.json');
  await fs.writeFile(jsonPath, JSON.stringify(components, null, 2), 'utf-8');

  // 写入 JS (ESM 导出)
  const jsPath = path.join(outputDir, 'components.js');
  const jsContent = `// 由 generate-from-html.mjs 自动生成\n// 时间：${new Date().toISOString()}\n\nexport const components = ${JSON.stringify(components, null, 2)};\n`;
  await fs.writeFile(jsPath, jsContent, 'utf-8');

  // 合并 CSS
  await mergeContentCssToOutput(contentDir, outputDir);
}

// 仅当直接运行时执行（被导入时不执行）
// 在不同平台/编码场景下，直接比较字符串可能失败，这里统一转换为 file URL 进行比较
const currentFileHref = pathToFileURL(process.argv[1] || '').href;
if (import.meta.url === currentFileHref) {
  main().catch((err) => {
    console.error('[generate-from-html] 运行出错：', err);
    process.exitCode = 1;
  });
}


