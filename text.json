{"id": "text", "type": "element/base", "updatedAt": 1752202309, "createdAt": 1752202309, "version": 1, "description": "文本组件，支持配置文本内容、链接、AI生成等功能", "standalone": false, "scope": "isolate", "props": {"text": "这里是占位文字", "href": {"type": "none", "value": "", "target": ""}, "prompt": "", "aiSet": {"contentKeywords": "生成一段${{公司名称}}${{行业}}的公司简介内容", "type": 1}, "dense": ""}, "propsMappings": {"text": {"dataType": ["string"], "defaultFieldBind": "text", "refType": "props, pageParams, dataSource, parentScope"}, "href": {"dataType": ["object"], "defaultFieldBind": "link", "refType": "props, pageParams, dataSource, parentScope"}, "prompt": {"dataType": ["string"], "defaultFieldBind": "placeholder", "refType": "props, pageParams, dataSource, parentScope"}}, "styles": {"*": ".${elemId} {\n    line-height: normal;\n}", "screen and (max-width:768px)": "", "screen and (min-width: 769px)": "", "screen and (max-width: 1024px) and (min-width: 769px)": ""}, "i18n": {"zh-CN": {"text": "文本组件", "description": "用于展示文本内容，支持链接配置和AI生成"}, "en-US": {"text": "Text Component", "description": "Display text content with link configuration and AI generation support"}}, "template": "<p class=\"${elemId} ${styleClass}\">\n    {{#if ${href}.value}}\n        <a href=\"{{$transferLink ${href} compId}}\" {{#eq ${href}.type 'email'}}rel=\"nofollow\"{{/eq}}{{#eq ${href}.type 'tel'}}rel=\"nofollow\"{{/eq}} target=\"{{${href}.target}}\">\n    {{/if}}\n    {{#if ${text}}}\n        {{$handleText ${text} ${dense}}}\n    {{^}}\n        {{${prompt}}}\n    {{/if}}\n    {{#if ${href}.value}}\n        </a>\n    {{/if}}\n</p>"}