{
    // 结构类型
    "schemaType": "page",
    // 更新时间
    "updatedAt": 1752202309,
    // 创建时间
    "createdAt": 1752202309,
    "version": 1,
    "sequence": 2,
    "page": {
        // 结构ID
        "id": "uuid",
        // 页面名称
        "pageName": "示例页面",
        // 文件名称
        "fileName": "example-page"
    },
    // 引用组件库
    // 组件库可以包含多个组件定义
    // 每个组件定义包含组件ID、类型、版本、描述、可配置属性
    // 在一个页面中如果同一个组件引用了多次，组件库中仅保留一份即可
    // 页面的结构信息放在 "schema" 数组中
    "components": {
        // 组件ID: 组件定义
        // 组件ID格式为: <组件类型>:<版本号>
        // 例如: "elementContainer:1" 表示元素容器组件的版本1
        // 组件类型可以是 "element/base" 或 "component/container" 等，会扩展新的组件类型
        // 组件版本号从1开始，每次调整逐步递增
        "elementContainer:1": {
            // 组件ID
            "id": "elementContainer",
            // 组件类型
            // 这里暂且定义为 `<category>/<name>` 的表达式结构，方便后续扩展
            "type": "component/element-container",
            // 更新时间
            "updatedAt": 1752202309,
            // 创建时间
            "createdAt": 1752202309,
            // 是否独立组件，表示该组件可以单独使用，不依赖其他组件
            // 如果为 true，则该组件可以在页面中独立使用
            // 如果为 false，则该组件需要依赖其他组件才能使用
            // 在使用数据源时，数据源的配置信息将全部绑定在此组件上，子组件绑定的数据源信息同样会写入到此组件中
            // 独立组件不允许嵌套
            "standalone": true,
            // 作用域类型，如果设置 isolate 则表示该组件的作用域是独立的
            // 在关联数据源字段时，子组件可以访问此组件的作用域内字段和父级数据源字段
            // 通常循环组件会设置为 isolate，这样可以确保每个循环实例的数据源独立
            "scope": "isolate",
            // 组件版本信息
            "version": 1,
            // 组件描述
            "description": "元素容器组件，内部可以放置其他元素，但不允许放置组件。",
            // 组件可配置属性
            "props": {
                "text": "This is a sample text component."
            },
            "propsMappings": {
                "text": {
                    // 组件支持的数据类型，除了基本数据类型，设计器会定义一些业务类型
                    // 例如：href 表示链接对象，file 标识文件对象等等
                    "dataType": [
                        "string",
                        "number",
                        "array",
                        "..."
                    ],
                    // 默认绑定的字段名，设置此字段在打开数据源面板后会默认选择这个字段（数据源如果有的话）
                    "defaultFieldBind": "name",
                    // 下面这三个字段是放在 "schema" 中定义的，放在这里是方便一起解释
                    // 引用类型，表示该字段可以引用的类型，目前支持（props 元素自身数据，pageParams URL 参数，dataSource 数据源，parentScope 父级作用域元素）                    
                    "refType": "props, pageParams, dataSource, parentScope",
                    // 引用 ID，表示该字段绑定的数据源 ID
                    "refId": "api:1",
                    // 绑定的字段名
                    "fieldBind": "name"
                }
            },
            // 样式配置
            "styles": {
                "*": "",
                "screen and (max-width:768px)": "",
                "screen and (min-width: 769px)": "",
                "screen and (max-width: 1024px) and (min-width: 769px)": "",
                // 上面四种样式是平台默认配置，也允许客户在这里继续追加配置，key 为媒体查询表达式
                // value 是字符串，保存完整的 css 代码，为了避免样式污染，通常需要使用 > 选择器做约束
                // 例如：.s_text > .text-element { color: red; }
                // 这样可以确保样式只作用于当前组件的子元素，而不会影响其他组件
                // value 存储原始样式的原因在于未来的兼容性，目前 css 已经允许使用变量和函数，也允许嵌套语法
                // 使用对象存储无法很好的满足未来扩展需求，在设计器源码模式中也不方便编辑
            },
            // 容器配置
            // 容器类型为 "component/container"，表示这是一个容器组件
            // 容器可以包含其他组件或元素，可通过规则配置
            "container": [
                {
                    "id": "cb:uuid"
                }
            ],
            // 国际化
            "i18n": {
                "zh-CN": {
                    "text": "元素容器"
                },
                "en-US": {
                    "text": "Element Container"
                }
            },
            // 组件模板代码
            "template": "<div class='element-container' data-id='{{id}}'>{{props.text}}</div>"
        },
        "text:1": {
            "id": "text",
            "type": "element/base",
            "updatedAt": 1752202309,
            "createdAt": 1752202309,
            "version": 1,
            "description": "文本组件",
            "props": {
                "type": "h1/h2/p",
                "text": "文本元素"
            },
            "template": "<{{props.type}} class='text-element' data-id='{{id}}'>{{props.text}}</{{props.type}}>"
        },
        "text:2": {
            "id": "text",
            "type": "element/base",
            "updatedAt": 1752202309,
            "createdAt": 1752202309,
            "version": 2,
            "description": "文本组件，升级版，支持配置跳转链接",
            "props": {
                "type": "h1/h2/p",
                "href": "https://example.com",
                "target": "_blank",
                "rel": "noopener noreferrer",
                "text": "文本元素"
            },
            "template": "<{{props.type}} class='text-element' data-id='{{id}}'>{{props.text}}</{{props.type}}>"
        }
    },
    "dataSources": {
        // 数据源可以包含多个数据源定义
        // 每个数据源定义包含数据源ID、类型、版本、描述、可配置属性
        // 数据源ID格式为: <数据源类型>:<版本号>
        // 例如: "api:1" 表示 API 数据源的版本1
        "api:1": {
            "id": "api",
            "name": "产品列表",
            "type": "dataSource/api",
            "dataType": "array",
            "version": 1,
            "description": "API 数据源",
            "props": {
                "url": "https://api.example.com/data",
                "method": "GET"
            },
            // 查询结构
            // 查询结构支持多级分组，使用 groupBegin 和 beginEnd 字段来标识逻辑组，如果逻辑组嵌套，则 groupBegin 可以设置为 1,2，结束组可以设置为 2, 1
            "query": [
                // 下组说明 id, category, name 在一组
                {
                    "field": "id", // 参与查询字段
                    "operator": "eq, neq, gt, gte, lt, lte, in, not in", // 筛选操作
                    "refType": "props, pageParams, dataSource, parentScope", // 略
                    "refId": "api:1", // 略
                    "fieldBind": "name", // 略
                    "groupBegin": "1" // 逻辑组开始
                },
                {
                    "field": "category",
                    "operator": "eq, neq, gt, gte, lt, lte, in, not in",
                    "refType": "props, pageParams, dataSource, parentScope",
                    "refId": "api:1",
                    "fieldBind": "name"
                },
                {
                    "field": "name",
                    "logic": "and, or",
                    "operator": "eq",
                    "value": "aaa",
                    "beginEnd": "1" // 逻辑组结束
                }
                // 简要说明多组语法
                // groupBegin: 1, 2
                // groupEnd: 2,
                // groupBegin: 3
                // groupEnd: 3, 1
                // 这种是大组中嵌套小组
            ],
            "sort": [ // 排序字段
                {
                    "field": "id", // 排序字段
                    "sort": "desc" // asc, desc
                }
            ]
        },
        // 下面展示的是静态数据源
        // 静态数据源与动态数据源结构类型，多了一个 data 属性，用于在页面中存储静态数据
        // data 变量的维护与循环体本身有关
        // 如果循环体内无元素，此时 data 结构如下，会有六个空对象
        // 如果在设计器中往循环体内拖入一个文本组件，此时 data 结构会变为一个包含六个文本组件的数组
        // 例如：
        //   "data": [
        //       { "text_1": "文本" },
        //       { "text_1": "文本" },
        //       { "text_1": "文本" },
        //       { "text_1": "文本" },
        //       { "text_1": "文本" },
        //       { "text_1": "文本" }
        //   ]
        // 命名方式为 "<props>_<element-id>" 避免多个元素同名冲突
        // 同理，如果往循环体内拖入一个子循环体，且子循环体内又放置了一个文本组件，此时结构如下：
        //   "data": [
        //       { 
        //           "text_1": "文本",
        //           "sub_loop_2": [
        //               { "text_3": "子循环文本" }
        //           ]
        //       },
        //       { 
        //           "text_1": "文本",
        //           "sub_loop_2": [
        //               { "text_3": "子循环文本" }
        //           ]
        //       },
        //       { 
        //           "text_1": "文本",
        //           "sub_loop_2": [
        //               { "text_3": "子循环文本" }
        //           ]
        //       },
        //       { 
        //           "text_1": "文本",
        //           "sub_loop_2": [
        //               { "text_3": "子循环文本" }
        //           ]
        //       },
        //       { 
        //           "text_1": "文本",
        //           "sub_loop_2": [
        //               { "text_3": "子循环文本" }
        //           ]
        //       },
        //       { 
        //           "text_1": "文本",
        //           "sub_loop_2": [
        //               { "text_3": "子循环文本" }
        //           ]
        //       }
        //   ]
        "loop-1:1": {
            "id": "loop-1",
            "name": "循环体",
            "version": "1", // 版本恒定 1
            "type": "static",
            "dataType": "array",
            "data": [
                {},
                {},
                {},
                {},
                {},
                {}
            ]
        }
    },
    // 页面结构信息
    // 结构信息是一个数组，每个元素表示一个组件或元素的实例，是一个扁平结构，根据 parentId 和 containerId 进行关联
    // parentId 决定了元素的父级关系，containerId 决定了元素所在的容器
    // 如果 parentId 为空，表示该元素是顶层元素
    // 每个元素都有一个唯一的实例 ID 和引用 ID，引用 ID 是组件库中定义的组件 ID
    // 
    // 实例结构合并：
    // 为了压缩 json 体积，多个实例可以共享同一个组件定义
    // 例如，多个文本元素可以共享同一个 "text" 组件定义，但每个实例可以有不同的配置信息
    // schema 中的组件实例信息会与组件库中的组件定义进行数据合并
    // 这样可以减少重复数据，提高性能和可维护性
    "schema": [
        {
            // 实例 ID
            "id": "1",
            // 引用 ID，引用组件库中的组件定义
            "refId": "elementContainer:1",
            // 组件配置，会与组件库中的组件定义进行数据合并
            "props": {
                "text": "文本元素"
            },
            // 容器配置，会与组件库中的组件定义进行数据合并
            "containers": [
                {
                    "id": "333b7de7a794451596f776cd48cd7c6e"
                }
            ]
        },
        {
            "id": "2",
            "refId": "text:1",
            // 父级组件 ID，如果该元素是顶层元素，则 parentId 为空
            "parentId": "1",
            // 容器 ID，表示该元素所在的容器位置
            // 如果该元素是顶层元素，则 containerId 为空
            "containerId": "333b7de7a794451596f776cd48cd7c6e",
            "props": {
                "text": "这是一个标题"
            },
            "propsMappings": {
                "text": {
                    "refType": "props, pageParams, dataSource, parentScope",
                    "refId": "api:1",
                    "fieldBind": "name"
                }
            }
        },
        {
            "id": "3",
            "refId": "text:2",
            "parentId": "1",
            "containerId": "333b7de7a794451596f776cd48cd7c6e",
            "props": {
                "text": "这是一个配置了链接的标题"
            }
        }
    ]
}