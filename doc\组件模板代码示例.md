# 元素模板代码示例

## 标准元素

标准元素用法。

示例 json 结构：

```json
{
    "id": "3",
    "refId": "text:2",
    "parentId": "1",
    "containerId": "********************************",
    "props": {
        "text": "这是一个配置了链接的标题",
        "href": {
            "type": "static", // 连接的类型有多种配置，比如 none, static, email, phone ...
            "href": "http://www.baidu.com",
            "target": "_self"
        }
    }
}
```

```html
<div class="{{ $class }} s_link" {{ $attrs }}>
    @if (!empty($data['href']['type']) && $data['href']['type'] != "none")
        <a class="s_a" {{ buildLinkAttrs($data['href']) }}>
    @endif
        <span class="s_text">{{ $data['text'] }}</span>    
    @if (!empty($data['href']['type']) && $data['href']['type'] != "none")
        </a>
    @endif
</div>
```

## 标准元素 - 绑定数据源

数据源配置：

```json
{
    "dataSources": {
        "api:1": {
            "id": "api",
            "name": "产品列表",
            "type": "dataSource/api",
            "dataType": "array",
            "version": 1,
            "description": "API 数据源",
            "props": {
                "url": "https://api.example.com/data",
                "method": "GET"
            }
        }
    }
}
```

示例 json 结构：

```json
{
    "id": "3",
    "refId": "text:2",
    "parentId": "1",
    "containerId": "********************************",
    "props": {
        "text": "这是一个配置了链接的标题",
        "href": {
            "type": "static", // 连接的类型有多种配置，比如 none, static, email, phone ...
            "href": "http://www.baidu.com",
            "target": "_self"
        }
    },
    "propsMappings": {
        "href": {
            "dataType": ["href"], // href 为约定类型，结构固定为 {"type": string, "href": string, "target": string}，这种类似结构应该还有很多
            "refType": "dataSource",
            "refId": "api:1",
            "fieldBind": "href"
        }
    }
}
```

模板代码，模板上不需要动，可以直接兼容。

```html
<div class="{{ $class }} s_link" {{ $attrs }}>
    @if (!empty($data['href']['type']) && $data['href']['type'] != "none")
        <a class="s_a" {{ buildLinkAttrs($data['href']) }}>
    @endif
        <span class="s_text">{{ $data['text'] }}</span>    
    @if (!empty($data['href']['type']) && $data['href']['type'] != "none")
        </a>
    @endif
</div>
```

## 标准元素 - 使用父级数据源

场景假设，此元素在循环体容器内，且循环体使用的是静态数据源。

示例 json 结构：

```json
{
    "id": "3",
    "refId": "text:2",
    "parentId": "1",
    "containerId": "********************************",
    "props": {
        "text": "这是一个配置了链接的标题",
        "href": {
            "type": "static", // 连接的类型有多种配置，比如 none, static, email, phone ...
            "href": "http://www.baidu.com",
            "target": "_self"
        }
    },
    "propsMappings": {
        "href": {
            "dataType": ["href"], // href 为约定类型，结构固定为 {"type": string, "href": string, "target": string}，这种类似结构应该还有很多
            "refType": "parentScope",
            "refId": "api:1",
            "fieldBind": "href_3" // 静态数据源绑定的字段，是当前字段加自身的 ID，避免属性冲突
        }
    }
}
```

模板代码，模板上不需要动，可以直接兼容。

```html
<div class="{{ $class }} s_link" {{ $attrs }}>
    @if (!empty($data['href']['type']) && $data['href']['type'] != "none")
        <a class="s_a" {{ buildLinkAttrs($data['href']) }}>
    @endif
        <span class="s_text">{{ $data['text'] }}</span>    
    @if (!empty($data['href']['type']) && $data['href']['type'] != "none")
        </a>
    @endif
</div>
```

## 标准元素 - 最简形式

略，用法同上，为了降低 dom 层级，避免无效元素嵌套，一个元素可以只包含一个 span 标签

```html
<span class="{{ $class }} s_text" {{ $attrs }}>{{ $data['text'] }}</span>
```

## 容器元素

容器元素，允许创建任意多的容器盒，标记有 `data-comp-type="box-item"` 的属性是允许放置子元素的。

容器渲染的时候可以使用 `slot` 自定义函数完成嵌套渲染。

`$context` 是上下文对象，用于获取改组件所有的原始信息。

示例 json 结构：

```json
{
    "id": "1",
    "refId": "elementContainer:1",
    "containers": [
        {
            "id": "********************************",
            // 根据容器的类型，这里可以追加其他参数
            // 例如：
            //   - Tab 页签类容器，有页签名称
            //   - 轮播图类容器
            //   - Grid 风格类容器
        },
        {
            "id": "bs80TE72ayUzw9JtULSvp3CK3LsYuuI2"
        }
    ]
}
```

模板代码：

```html
<div class="{{ $class }} s_container" {{ $attrs }}>
    @foreach ($container as $containers)
        <div data-id="{{ $container['id'] }}" data-comp-type="box-item">{{ slot($context, $container) }}</div>
    @endforeach
</div>
```

## 容器元素 - 拥有独立作用域

容器元素，容器盒的数量与 `$data['data']` 的数组长度有关，标记有 `data-comp-type="box-item"` 的属性是允许放置子元素的。

区别在于，如果设计器中有多个容器盒，操作任意一个盒内元素，其他盒内元素都会一起边，因为数据是遍历出来的。

容器渲染的时候可以使用 `scopedSlot` 自定义函数完成嵌套渲染，此时子元素的 `$data` 变量值会在元素绑定属性类型为 `parent` 时变为 `$item` 变量。

数据源配置：

```json
{
    "dataSources": {
        "loop-1:1": {
            "id": "loop-1",
            "name": "循环体",
            "version": "1", // 版本恒定 1
            "type": "static",
            "data": [
                [],
                [],
                [],
                [],
                [],
                [],
            ]
        }
    }
}
```

组件 JSON 配置：

```json
{
    "id": "1",
    "refId": "elementContainer:1",
    "props": {
        "data": [
            [],
            [],
            [],
            [],
            [],
            [],
        ]
    },
    "propsMappings": {
        "data": {
            "refType": "dataSource",
            "refId": "api:2",
            "fieldBind": "data"
        }
    }
}
```

组件模板：

```html
<div class="{{ $class }} s_loop" {{ $attrs }}>
    @foreach ($item as $data['data'])
        <div data-comp-type="box-item">{{ scopedSlot($context, $item) }}</div>
    @endforeach
</div>
```

