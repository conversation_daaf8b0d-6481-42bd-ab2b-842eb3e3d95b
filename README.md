### 项目使用说明

#### 目标
从 `index.html` 解析页面元素，结合 `content/` 资源，生成标准化组件数据与脚本至 `output/`。

#### 目录结构
- `index.html`：源页面（输入）
- `content/`：样式与可扩展组件资源（输入）
  - `global.css`、`pc.css`、`pad.css`、`mobile.css`
  - `components.js`（预留，当前不依赖）
- `text.json`：组件结构参考模板（输入）
- `generate-from-html.mjs`：生成脚本
- `output/`：生成产物目录（输出）

#### 环境要求
- Node.js ≥ 14（原生 ESM，免安装依赖）
- Windows/macOS/Linux 均可

#### 快速使用
1) 将要解析的页面写入 `index.html`
2) 在项目根目录执行：
```bash
node generate-from-html.mjs
```
3) 生成结果查看：
- `output/components.json`
- `output/components.js`
- `output/styles.css`

#### 解析规则（默认）
- 优先匹配带有 `data-comp="text"` 的元素
- 回退匹配常见文本类标签：`p`、`span`、`a`、`button`、`h1`~`h6`
- 文本内容：取元素内的纯文本
- 链接处理：若存在 `href`/`target`，将映射至 `props.href.{value,type,target}`
  - `mailto:` → `email`，`tel:` → `tel`，其他为 `url`，无链接为 `none`
- 生成的组件结构参考 `text.json`

#### 输出内容
- `output/components.json`：组件数组（JSON）
- `output/components.js`：同数组的 ESM 导出，形如 `export const components = [...]`
- `output/styles.css`：合并 `content/*.css`（合并顺序：`global.css` → `pc.css` → `pad.css` → `mobile.css` → 其他）

#### 常见问题
- 输出为空：检查 `index.html` 是否包含可匹配的元素（`data-comp="text"` 或上述文本类标签）
- 样式为空：检查 `content/` 下是否存在对应 `.css` 文件并有内容

#### 备注
- 当前脚本聚焦“文本类组件”；如需扩展 `img`、列表、表单等组件的生成逻辑，可在后续版本中追加。


